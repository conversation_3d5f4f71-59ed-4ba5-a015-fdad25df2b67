-- Global variable module is only used to define global variables and module functions. The motion command cannot be called here.
-- Version: Lua 5.3.5
-- 物料分拣系统全局函数库（新版本）
-- 包含所有可重用的函数和变量定义

-- ================== 全局变量 ==================
local ip = "************"
local port1 = 6005  -- 检测区1端口
local port2 = 6006  -- 检测区2端口
local err = 0
local socket1 = 0   -- 检测区1连接
local socket2 = 0   -- 检测区2连接

-- 传送带控制参数
local CONVEYOR_DETECTION1_POS = 0      -- 检测区1位置（mm）
local CONVEYOR_DETECTION2_POS = 500    -- 检测区2位置（mm）
local CONVEYOR_SPEED = 80               -- 传送带运动速度比例（1-100）
local CONVEYOR_ACC = 50                 -- 传送带运动加速度比例（1-100）

-- 系统状态变量
local current_conveyor_pos = 0          -- 当前传送带位置
local system_initialized = false       -- 系统初始化状态

-- ================== TCP通信函数 ==================

-- 连接检测区1视觉服务器
function connect_detection1()
    ::create_socket1::
    err, socket1 = TCPCreate(false, ip, port1)
    if err ~= 0 then
        print("无法创建检测区1 socket，正在重新连接")
        Sleep(1000)
        goto create_socket1
    end
    err = TCPStart(socket1, 0)
    if err ~= 0 then
        print("无法连接检测区1服务器，正在重新连接")
        TCPDestroy(socket1)
        Sleep(1000)
        goto create_socket1
    end
    print("检测区1 TCP连接建立成功 (端口:" .. port1 .. ")")
end

-- 连接检测区2视觉服务器
function connect_detection2()
    ::create_socket2::
    err, socket2 = TCPCreate(false, ip, port2)
    if err ~= 0 then
        print("无法创建检测区2 socket，正在重新连接")
        Sleep(1000)
        goto create_socket2
    end
    err = TCPStart(socket2, 0)
    if err ~= 0 then
        print("无法连接检测区2服务器，正在重新连接")
        TCPDestroy(socket2)
        Sleep(1000)
        goto create_socket2
    end
    print("检测区2 TCP连接建立成功 (端口:" .. port2 .. ")")
end

-- 初始化所有TCP连接
function start()
    print("正在建立TCP连接...")
    connect_detection1()
    connect_detection2()
    print("所有TCP连接建立完成")
end

-- 发送指令到检测区1
function send_command_to_detection1(command)
    local msg = command
    TCPWrite(socket1, msg)
    print("已发送指令到检测区1: " .. command)
end

-- 发送指令到检测区2
function send_command_to_detection2(command)
    local msg = command
    TCPWrite(socket2, msg)
    print("已发送指令到检测区2: " .. command)
end

-- 关闭所有TCP连接
function close_tcp_connection()
    if socket1 and socket1 ~= 0 then
        TCPDestroy(socket1)
        print("检测区1 TCP连接已关闭")
    end
    if socket2 and socket2 ~= 0 then
        TCPDestroy(socket2)
        print("检测区2 TCP连接已关闭")
    end
end

-- 兼容性函数
function send_command(command)
    send_command_to_detection1(command)
end

-- ================== 工具函数 ==================

function split(str, delimiter)
    if str == nil or str == '' then
        return {}
    end
    local result = {}
    for token in string.gmatch(str, '[^' .. delimiter .. ']+') do
        table.insert(result, token)
    end
    return result
end

function safe_sleep(ms)
    if ms > 0 then
        Sleep(ms)
    end
end

function log_message(level, message)
    local timestamp = os.date("%H:%M:%S")
    print("[" .. timestamp .. "] " .. level .. ": " .. message)
end

-- ================== 传送带控制函数 ==================

function move_conveyor_to_position(target_pos)
    log_message("INFO", "移动传送带到位置: " .. target_pos .. "mm")
    
    -- 检查目标位置是否合理
    if target_pos < 0 or target_pos > 1000 then
        log_message("ERROR", "传送带目标位置超出范围: " .. target_pos)
        return false
    end
    
    -- 使用MovJExt指令控制传送带
    local option = {SpeedE = CONVEYOR_SPEED, AccE = CONVEYOR_ACC, SYNC = 1}
    MovJExt(target_pos, option)
    SyncAll()  -- 确保运动完成
    
    current_conveyor_pos = target_pos
    log_message("INFO", "传送带已到达位置: " .. target_pos .. "mm")
    return true
end

function move_conveyor_to_detection1()
    return move_conveyor_to_position(CONVEYOR_DETECTION1_POS)
end

function move_conveyor_to_detection2()
    return move_conveyor_to_position(CONVEYOR_DETECTION2_POS)
end

function get_current_conveyor_position()
    return current_conveyor_pos
end

function set_conveyor_speed(speed, acc)
    if speed >= 1 and speed <= 100 then
        CONVEYOR_SPEED = speed
        log_message("INFO", "传送带速度设置为: " .. speed)
    end
    if acc >= 1 and acc <= 100 then
        CONVEYOR_ACC = acc
        log_message("INFO", "传送带加速度设置为: " .. acc)
    end
end

-- ================== 视觉系统通信函数 ==================

function trigger_vision_detection1()
    send_command_to_detection1("ok")
    log_message("INFO", "等待检测区1视觉系统返回坐标...")

    local timeout_count = 0
    local max_timeout = 100  -- 最大等待次数（5秒）

    while timeout_count < max_timeout do
        err, buf = TCPRead(socket1, 1000, "string")
        if err == 0 and buf and buf.buf and buf.buf ~= '' then
            log_message("INFO", "接收到检测区1数据: " .. buf.buf)
            local parts = split(buf.buf, ";")
            if #parts >= 4 then
                local id = parts[1]  -- 保持为字符串，可能是数字或"?"
                local x = tonumber(parts[2])
                local y = tonumber(parts[3])
                local r = tonumber(parts[4])
                if id and x and y and r then
                    log_message("INFO", string.format("解析成功 - ID:%s, X:%.2f, Y:%.2f, R:%.2f", tostring(id), x, y, r))
                    return id, x, y, r  -- id保持原始格式
                end
            end
            log_message("WARN", "数据格式错误，重新等待...")
        end
        timeout_count = timeout_count + 1
        Sleep(50)
    end

    log_message("ERROR", "检测区1检测超时")
    return nil, nil, nil, nil
end

function trigger_vision_detection2()
    send_command_to_detection2("start")
    log_message("INFO", "等待检测区2视觉系统返回OK/NG结果...")

    local timeout_count = 0
    local max_timeout = 100  -- 最大等待次数（5秒）

    while timeout_count < max_timeout do
        err, buf = TCPRead(socket2, 1000, "string")
        if err == 0 and buf and buf.buf and buf.buf ~= '' then
            log_message("INFO", "接收到检测区2结果: " .. buf.buf)
            local result = string.upper(string.gsub(buf.buf, "%s+", ""))
            if result == "OK" or result == "NG" then
                log_message("INFO", "检测区2结果: " .. result)
                return result
            end
            log_message("WARN", "结果格式错误，重新等待...")
        end
        timeout_count = timeout_count + 1
        Sleep(50)
    end

    log_message("ERROR", "检测区2检测超时")
    return nil
end

-- ================== 机械臂控制函数 ==================

function safe_move_j(point, options)
    options = options or "SYNC=1"
    MovJ(point, options)
    safe_sleep(100)
end

function safe_move_l(point, options)
    options = options or "SYNC=1"
    MovL(point, options)
    safe_sleep(100)
end

function pick_material(x, y, z, r)
    log_message("INFO", "开始抓取物料...")
    
    -- 定义抓取点位
    local pick_pos_safe = {coordinate = {x, y, z + 50, r}, tool = 1, user = 0}
    local pick_pos = {coordinate = {x, y, z, r}, tool = 1, user = 0}
    
    -- 抓取动作序列
    DO(1, 1)  -- 确保吸盘开启
    safe_move_l(pick_pos_safe)  -- 移动到安全高度
    safe_sleep(200)
    safe_move_l(pick_pos)       -- 下降到抓取位置
    safe_sleep(500)             -- 等待吸附
    safe_move_l(pick_pos_safe)  -- 提升到安全高度
    safe_sleep(200)
    
    log_message("INFO", "物料抓取完成")
    return true
end

function place_material(target_point, r)
    log_message("INFO", "开始放置物料...")
    
    -- 放置动作序列
    safe_move_j(RP(target_point, {0, 0, 50, r}))  -- 移动到目标点上方
    safe_sleep(300)
    safe_move_l(target_point)                     -- 下降到放置位置
    safe_sleep(200)
    DO(1, 0)                                      -- 关闭吸盘
    safe_sleep(300)
    DO(2, 1)                                      -- 打开喷气
    safe_sleep(500)
    DO(2, 0)                                      -- 关闭喷气
    safe_move_l(RP(target_point, {0, 0, 50, r})) -- 提升到安全高度
    safe_sleep(200)
    
    log_message("INFO", "物料放置完成")
    return true
end

-- ================== 系统初始化函数 ==================

function initialize_system()
    log_message("INFO", "开始系统初始化...")
    
    -- 初始化TCP连接
    start()
    
    -- 初始化IO端口
    DO(2, 0)  -- 关闭气泵
    DO(1, 1)  -- 打开吸盘（准备状态）
    log_message("INFO", "IO端口初始化完成")
    
    -- 设置系统状态
    system_initialized = true
    log_message("INFO", "系统初始化完成")
    
    return true
end

function shutdown_system()
    log_message("INFO", "开始系统关闭...")
    
    -- 关闭所有IO
    DO(1, 0)  -- 关闭吸盘
    DO(2, 0)  -- 关闭气泵
    
    -- 关闭TCP连接
    close_tcp_connection()
    
    system_initialized = false
    log_message("INFO", "系统已安全关闭")
end

-- ================== 兼容性函数 ==================

-- 保持与原代码的兼容性
function fa(thing)
    send_command(thing)
end

function read_and_parse_vision_data()
    return trigger_vision_detection1()
end

-- ================== 配置函数 ==================

function set_conveyor_positions(detection1_pos, detection2_pos)
    if detection1_pos >= 0 and detection1_pos <= 1000 then
        CONVEYOR_DETECTION1_POS = detection1_pos
        log_message("INFO", "检测区1位置设置为: " .. detection1_pos .. "mm")
    end
    if detection2_pos >= 0 and detection2_pos <= 1000 then
        CONVEYOR_DETECTION2_POS = detection2_pos
        log_message("INFO", "检测区2位置设置为: " .. detection2_pos .. "mm")
    end
end

function get_system_status()
    return {
        initialized = system_initialized,
        detection1_connected = (socket1 and socket1 ~= 0),
        detection2_connected = (socket2 and socket2 ~= 0),
        conveyor_position = current_conveyor_pos,
        conveyor_speed = CONVEYOR_SPEED,
        conveyor_acc = CONVEYOR_ACC
    }
end
