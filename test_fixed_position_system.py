#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的绝对位置系统
演示真正的绝对位置控制
"""

import customtkinter as ctk
from 门 import ConveyorPositionManager

class MockRobotApp:
    """模拟主程序类，用于测试修复后的位置系统"""
    
    def __init__(self):
        self.current_conveyor_position = 0.0  # 逻辑位置（可被标定重置）
        self.absolute_conveyor_position = 0.0  # 绝对位置（实际物理位置）
        self.conveyor_total_length = 850.0  # 滑轨实际长度
        self.is_robot_connected = True  # 模拟已连接状态
        self.motion_socket = None  # 模拟socket
        
    def log(self, message, color="white"):
        """模拟日志输出"""
        print(f"[{color}] {message}")
        
    def update_conveyor_display(self):
        """模拟更新显示"""
        print(f"位置更新 - 逻辑: {self.current_conveyor_position:.2f} mm, 绝对: {self.absolute_conveyor_position:.2f} mm")

def test_fixed_position_system():
    """测试修复后的位置系统"""
    
    # 创建测试窗口
    root = ctk.CTk()
    root.title("修复后的绝对位置系统测试 - V17.0")
    root.geometry("900x800")
    
    # 创建模拟主程序
    mock_app = MockRobotApp()
    
    # 创建位置管理器
    position_manager = ConveyorPositionManager(mock_app)
    
    # 主框架
    main_frame = ctk.CTkScrollableFrame(root, label_text="测试控制面板")
    main_frame.pack(padx=10, pady=10, fill="both", expand=True)
    
    # 位置显示区域
    display_frame = ctk.CTkFrame(main_frame)
    display_frame.pack(pady=10, padx=10, fill="x")
    
    ctk.CTkLabel(display_frame, text="当前位置显示", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)
    
    logic_pos_label = ctk.CTkLabel(display_frame, text="逻辑位置: 0.00 mm", font=ctk.CTkFont(size=14))
    logic_pos_label.pack(pady=2)
    
    absolute_pos_label = ctk.CTkLabel(display_frame, text="绝对位置: 0.00 mm", font=ctk.CTkFont(size=14), text_color="lightgreen")
    absolute_pos_label.pack(pady=2)
    
    # 控制按钮区域
    control_frame = ctk.CTkFrame(main_frame)
    control_frame.pack(pady=10, padx=10, fill="x")
    
    ctk.CTkLabel(control_frame, text="移动控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
    
    def simulate_move_step(distance):
        """模拟步进移动"""
        old_abs = mock_app.absolute_conveyor_position
        mock_app.current_conveyor_position += distance
        mock_app.absolute_conveyor_position += distance
        
        # 限制范围
        if mock_app.absolute_conveyor_position < 0:
            mock_app.absolute_conveyor_position = 0
        elif mock_app.absolute_conveyor_position > mock_app.conveyor_total_length:
            mock_app.absolute_conveyor_position = mock_app.conveyor_total_length
            
        update_display()
        mock_app.log(f"步进移动 {distance} mm，绝对位置: {old_abs:.2f} → {mock_app.absolute_conveyor_position:.2f}")
    
    def simulate_move_to_absolute(target_pos):
        """模拟移动到绝对位置"""
        if target_pos < 0 or target_pos > mock_app.conveyor_total_length:
            mock_app.log(f"目标位置 {target_pos} mm 超出范围 (0-{mock_app.conveyor_total_length} mm)", "red")
            return
            
        old_abs = mock_app.absolute_conveyor_position
        distance = target_pos - old_abs
        mock_app.current_conveyor_position += distance
        mock_app.absolute_conveyor_position = target_pos
        
        update_display()
        mock_app.log(f"移动到绝对位置 {target_pos} mm (移动距离: {distance:.2f} mm)", "green")
    
    def calibrate_logic_zero():
        """标定逻辑零点"""
        mock_app.log(f"标定逻辑零点，绝对位置保持: {mock_app.absolute_conveyor_position:.2f} mm", "yellow")
        mock_app.current_conveyor_position = 0.0
        update_display()
    
    def reset_absolute_zero():
        """重置绝对零点"""
        mock_app.log("重置绝对零点，所有记录位置将失效！", "orange")
        mock_app.absolute_conveyor_position = 0.0
        mock_app.current_conveyor_position = 0.0
        update_display()
    
    def update_display():
        """更新位置显示"""
        logic_pos_label.configure(text=f"逻辑位置: {mock_app.current_conveyor_position:.2f} mm")
        absolute_pos_label.configure(text=f"绝对位置: {mock_app.absolute_conveyor_position:.2f} mm")
    
    # 步进移动按钮
    step_btn_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
    step_btn_frame.pack(pady=5, fill="x")
    
    ctk.CTkButton(step_btn_frame, text="前进50mm", command=lambda: simulate_move_step(50)).pack(side="left", padx=5)
    ctk.CTkButton(step_btn_frame, text="后退50mm", command=lambda: simulate_move_step(-50)).pack(side="left", padx=5)
    ctk.CTkButton(step_btn_frame, text="前进100mm", command=lambda: simulate_move_step(100)).pack(side="left", padx=5)
    
    # 绝对位置移动
    abs_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
    abs_frame.pack(pady=5, fill="x")
    
    ctk.CTkLabel(abs_frame, text="移动到绝对位置:").pack(side="left", padx=5)
    abs_entry = ctk.CTkEntry(abs_frame, placeholder_text="0-850", width=100)
    abs_entry.pack(side="left", padx=5)
    
    def move_to_abs_input():
        try:
            target = float(abs_entry.get())
            simulate_move_to_absolute(target)
        except ValueError:
            mock_app.log("请输入有效的数字", "red")
    
    ctk.CTkButton(abs_frame, text="移动", command=move_to_abs_input).pack(side="left", padx=5)
    ctk.CTkButton(abs_frame, text="回零点", command=lambda: simulate_move_to_absolute(0)).pack(side="left", padx=5)
    
    # 快速位置按钮
    quick_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
    quick_frame.pack(pady=5, fill="x")
    
    ctk.CTkButton(quick_frame, text="→ 200mm", command=lambda: simulate_move_to_absolute(200)).pack(side="left", padx=5)
    ctk.CTkButton(quick_frame, text="→ 400mm", command=lambda: simulate_move_to_absolute(400)).pack(side="left", padx=5)
    ctk.CTkButton(quick_frame, text="→ 600mm", command=lambda: simulate_move_to_absolute(600)).pack(side="left", padx=5)
    ctk.CTkButton(quick_frame, text="→ 800mm", command=lambda: simulate_move_to_absolute(800)).pack(side="left", padx=5)
    
    # 标定按钮
    calib_btn_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
    calib_btn_frame.pack(pady=5, fill="x")
    
    ctk.CTkButton(calib_btn_frame, text="标定逻辑零点", command=calibrate_logic_zero).pack(side="left", padx=5)
    ctk.CTkButton(calib_btn_frame, text="重置绝对零点", command=reset_absolute_zero, 
                 fg_color="orange", hover_color="darkorange").pack(side="left", padx=5)
    
    # 位置记忆UI
    position_manager.create_position_ui(main_frame)
    
    # 说明文本
    info_frame = ctk.CTkFrame(main_frame)
    info_frame.pack(pady=10, padx=10, fill="both", expand=True)
    
    ctk.CTkLabel(info_frame, text="测试说明", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
    
    info_text = """
修复后的功能特点：

1. 绝对位置控制：
   - 输入200mm，滑轨直接移动到200mm位置
   - 输入0mm，滑轨直接回到零点
   - 绝对位置显示当前实际物理位置

2. 位置记录系统：
   - 记录的是当前绝对位置
   - 移动到记录位置时直接定位，不受标定影响
   - 支持范围检查（0-850mm）

3. 测试步骤：
   a) 使用快速位置按钮移动到不同位置
   b) 记录几个位置（如检测区一记录在200mm）
   c) 标定逻辑零点（注意绝对位置不变）
   d) 点击"移动到此"测试是否能正确回到记录位置
   e) 使用绝对位置输入框测试直接定位功能

4. 问题修复：
   - 滑轨长度修正为850mm
   - 绝对位置不再是累加值，而是实际位置
   - 移动到记录位置功能已修复
   - 添加了范围检查，防止超出滑轨范围
    """
    
    info_textbox = ctk.CTkTextbox(info_frame, height=200)
    info_textbox.pack(pady=5, padx=5, fill="both", expand=True)
    info_textbox.insert("0.0", info_text)
    info_textbox.configure(state="disabled")
    
    # 初始显示更新
    update_display()
    
    # 运行测试
    root.mainloop()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    test_fixed_position_system()
