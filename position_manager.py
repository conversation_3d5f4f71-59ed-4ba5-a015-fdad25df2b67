#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
位置记录和验证管理器
管理四个关键点位的记录、验证和数据持久化
"""

import json
import os
from datetime import datetime
from typing import Dict, Optional, Tuple

class PositionPoint:
    """单个位置点的数据结构"""
    
    def __init__(self, name: str, position: float = None, recorded_time: str = None):
        self.name = name
        self.position = position
        self.recorded_time = recorded_time
        self.is_recorded = position is not None
        
    def record_position(self, position: float):
        """记录位置"""
        self.position = position
        self.recorded_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.is_recorded = True
        
    def clear_record(self):
        """清除记录"""
        self.position = None
        self.recorded_time = None
        self.is_recorded = False
        
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "name": self.name,
            "position": self.position,
            "recorded_time": self.recorded_time,
            "is_recorded": self.is_recorded
        }
        
    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建实例"""
        point = cls(data["name"])
        point.position = data.get("position")
        point.recorded_time = data.get("recorded_time")
        point.is_recorded = data.get("is_recorded", False)
        return point

class PositionManager:
    """位置记录和验证管理器"""
    
    def __init__(self, config_file: str = "position_records.json"):
        self.config_file = config_file
        self.tolerance_mm = 1.0  # 位置容差 ±1mm
        
        # 初始化四个关键点位
        self.points = {
            "detection_zone_1": PositionPoint("检测区一"),
            "detection_zone_2": PositionPoint("检测区二"),
            "assembly_zone_1": PositionPoint("装配区一"),
            "assembly_zone_2": PositionPoint("装配区二")
        }
        
        # 加载已保存的位置数据
        self.load_positions()
        
    def record_position(self, zone_key: str, current_position: float) -> bool:
        """记录指定区域的位置"""
        if zone_key not in self.points:
            return False
            
        self.points[zone_key].record_position(current_position)
        self.save_positions()
        return True
        
    def get_position(self, zone_key: str) -> Optional[float]:
        """获取指定区域的记录位置"""
        if zone_key not in self.points:
            return None
        return self.points[zone_key].position
        
    def is_recorded(self, zone_key: str) -> bool:
        """检查指定区域是否已记录"""
        if zone_key not in self.points:
            return False
        return self.points[zone_key].is_recorded
        
    def get_record_time(self, zone_key: str) -> Optional[str]:
        """获取记录时间"""
        if zone_key not in self.points:
            return None
        return self.points[zone_key].recorded_time
        
    def validate_position(self, zone_key: str, actual_position: float) -> Tuple[bool, float]:
        """验证位置精度
        
        Returns:
            (is_valid, error): 是否在容差范围内，位置误差
        """
        expected_position = self.get_position(zone_key)
        if expected_position is None:
            return False, float('inf')
            
        error = abs(actual_position - expected_position)
        is_valid = error <= self.tolerance_mm
        return is_valid, error
        
    def get_all_recorded_count(self) -> int:
        """获取已记录的点位数量"""
        return sum(1 for point in self.points.values() if point.is_recorded)
        
    def is_all_recorded(self) -> bool:
        """检查是否所有点位都已记录"""
        return self.get_all_recorded_count() == 4
        
    def clear_all_records(self):
        """清除所有记录"""
        for point in self.points.values():
            point.clear_record()
        self.save_positions()
        
    def get_zone_display_name(self, zone_key: str) -> str:
        """获取区域显示名称"""
        zone_names = {
            "detection_zone_1": "检测区一",
            "detection_zone_2": "检测区二", 
            "assembly_zone_1": "装配区一",
            "assembly_zone_2": "装配区二"
        }
        return zone_names.get(zone_key, zone_key)
        
    def get_zone_keys(self) -> list:
        """获取所有区域键值"""
        return list(self.points.keys())
        
    def save_positions(self):
        """保存位置数据到文件"""
        try:
            data = {
                "version": "1.0",
                "saved_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "tolerance_mm": self.tolerance_mm,
                "positions": {key: point.to_dict() for key, point in self.points.items()}
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存位置数据失败: {e}")
            
    def load_positions(self):
        """从文件加载位置数据"""
        try:
            if not os.path.exists(self.config_file):
                return
                
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 加载容差设置
            self.tolerance_mm = data.get("tolerance_mm", 1.0)
            
            # 加载位置数据
            positions_data = data.get("positions", {})
            for key, point_data in positions_data.items():
                if key in self.points:
                    self.points[key] = PositionPoint.from_dict(point_data)
                    
        except Exception as e:
            print(f"加载位置数据失败: {e}")
            
    def export_positions(self, filename: str = None) -> bool:
        """导出位置数据"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"position_backup_{timestamp}.json"
            
        try:
            data = {
                "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "version": "1.0",
                "tolerance_mm": self.tolerance_mm,
                "positions": {key: point.to_dict() for key, point in self.points.items()}
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            return True
        except Exception as e:
            print(f"导出位置数据失败: {e}")
            return False
            
    def import_positions(self, filename: str) -> bool:
        """导入位置数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 导入容差设置
            self.tolerance_mm = data.get("tolerance_mm", 1.0)
            
            # 导入位置数据
            positions_data = data.get("positions", {})
            for key, point_data in positions_data.items():
                if key in self.points:
                    self.points[key] = PositionPoint.from_dict(point_data)
                    
            self.save_positions()
            return True
        except Exception as e:
            print(f"导入位置数据失败: {e}")
            return False
            
    def get_summary(self) -> dict:
        """获取位置记录摘要"""
        summary = {
            "total_points": len(self.points),
            "recorded_points": self.get_all_recorded_count(),
            "completion_rate": f"{(self.get_all_recorded_count() / len(self.points) * 100):.1f}%",
            "tolerance_mm": self.tolerance_mm,
            "details": {}
        }
        
        for key, point in self.points.items():
            summary["details"][key] = {
                "name": point.name,
                "is_recorded": point.is_recorded,
                "position": point.position,
                "recorded_time": point.recorded_time
            }
            
        return summary

# 全局位置管理器实例
position_manager = PositionManager()

if __name__ == "__main__":
    # 测试代码
    pm = PositionManager()
    
    print("🧪 位置管理器测试")
    print("=" * 40)
    
    # 测试记录位置
    pm.record_position("detection_zone_1", 150.5)
    pm.record_position("detection_zone_2", 350.2)
    
    print(f"检测区一位置: {pm.get_position('detection_zone_1')} mm")
    print(f"检测区二位置: {pm.get_position('detection_zone_2')} mm")
    print(f"已记录点位: {pm.get_all_recorded_count()}/4")
    
    # 测试验证
    is_valid, error = pm.validate_position("detection_zone_1", 150.8)
    print(f"位置验证: 有效={is_valid}, 误差={error:.2f}mm")
    
    # 显示摘要
    summary = pm.get_summary()
    print(f"完成率: {summary['completion_rate']}")
