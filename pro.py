# =================================================================================
#  机器人视觉控制系统 - V17.1 (纯TCP控制版本)
# =================================================================================
import customtkinter as ctk
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os
import ctypes
# import requests # <--- 已移除
import json
from position_manager import position_manager

# --- 1. 全局配置 ---
ROBOT_IP = "***********"
PYTHON_PC_IP = "*************"
DASHBOARD_PORT = 29999
MOTION_PORT = 30003
VISION_SERVER_PORT = 6005
GRIPPER_IO_INDEX = 1
VISION_TRIGGER_PORT = 6006
VISION_TRIGGER_CMD = "TRIGGER"


# ▼▼▼▼▼ 【Dobot 滑轨控制器 (纯TCP模式) - 已精简】 ▼▼▼▼▼
class DobotSlideRailController:
    """Dobot滑轨精确控制器 (纯TCP模式)"""

    def __init__(self, robot_ip="***********", tcp_port=30003):
        self.robot_ip = robot_ip
        self.tcp_port = tcp_port
        self.current_position = 0.0
        self.slide_range = 850.0  # 滑轨总长度
        self.is_connected = False

    def connect(self):
        """连接到Dobot控制器 - 纯TCP诊断版本"""
        print(f"[SLIDE] 正在测试连接到 {self.robot_ip}...")

        # 1. 测试基础网络连通性
        try:
            print(f"[SLIDE] 测试基础网络连通性...")
            import subprocess
            # 使用-n 1发送1个包，-w 2000设置2秒超时
            result = subprocess.run(
                ['ping', '-n', '1', '-w', '2000', self.robot_ip],
                capture_output=True, text=True, timeout=3
            )
            if result.returncode == 0:
                print(f"[SLIDE] ✅ 网络连通性正常")
            else:
                print(f"[SLIDE] ⚠️ 网络连通性测试失败: {result.stderr}")
                self.is_connected = False
                return False
        except Exception as e:
            print(f"[SLIDE] ⚠️ 网络连通性测试异常: {e}")
            self.is_connected = False
            return False

        # 2. 测试TCP连接
        try:
            print(f"[SLIDE] 测试TCP连接 {self.robot_ip}:{self.tcp_port}...")
            # 使用 create_connection 进行连接测试，超时设置为5秒
            test_socket = socket.create_connection((self.robot_ip, self.tcp_port), timeout=5)
            print(f"[SLIDE] ✅ TCP连接建立成功")
            test_socket.close()
            self.is_connected = True
            return True
        except socket.timeout:
            print(f"[SLIDE] ❌ TCP连接超时")
        except ConnectionRefusedError:
            print(f"[SLIDE] ❌ TCP连接被拒绝 - 端口 {self.tcp_port} 可能未开放或服务未启动")
        except Exception as e:
            print(f"[SLIDE] ❌ TCP连接异常: {e}")

        self.is_connected = False
        print(f"[SLIDE] ❌ TCP连接方式失败")
        return False

    def move_to_absolute_position(self, position_mm, speed=50, accel=50):
        """移动到绝对位置"""
        if not self.is_connected:
            print("[SLIDE] 移动失败：滑轨控制器未连接")
            return False

        if not (0 <= position_mm <= self.slide_range):
            print(f"[SLIDE] 移动失败：目标位置 {position_mm}mm 超出范围")
            return False

        try:
            # 使用MovJExt指令控制扩展轴 (通常滑轨作为扩展轴)
            cmd = f"MovJExt({position_mm},{{SpeedE={speed},AccE={accel},SYNC=1}})"
            success = self._send_command_tcp(cmd)

            if success:
                self.current_position = position_mm

            return success
        except Exception as e:
            print(f"移动到绝对位置失败: {e}")
            return False

    def move_relative(self, distance_mm, speed=50, accel=50):
        """相对移动"""
        target_position = self.current_position + distance_mm
        return self.move_to_absolute_position(target_position, speed, accel)

    def get_current_position(self):
        """获取当前位置"""
        return self.current_position

    def emergency_stop(self):
        """急停"""
        try:
            # 使用Pause()指令进行急停
            cmd = "Pause()"
            return self._send_command_tcp(cmd)
        except:
            return False

    def _send_command_tcp(self, command, timeout=10, retry_count=3):
        """通过TCP发送指令 - 增强版本，支持重试和更长超时"""
        for attempt in range(retry_count):
            try:
                print(f"[SLIDE] 尝试 {attempt + 1}/{retry_count}: 发送指令 '{command}' 到 {self.robot_ip}:{self.tcp_port}")

                with socket.create_connection((self.robot_ip, self.tcp_port), timeout=timeout) as sock:
                    sock.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                    full_cmd = command + "\n"
                    sock.sendall(full_cmd.encode('utf-8'))
                    sock.settimeout(timeout)
                    response = sock.recv(1024).decode('utf-8').strip()
                    print(f"[SLIDE] 收到响应: {response}")

                    if response:
                        parts = response.split(',')
                        if len(parts) > 0 and parts[0] == '0':
                            print(f"[SLIDE] 指令执行成功")
                            return True
                        else:
                            error_code = parts[0] if parts else 'unknown'
                            print(f"[SLIDE] 指令执行失败，错误码: {error_code}")
                            # 你可以在这里添加对特定错误码的处理
                            return False
                    else:
                        print(f"[SLIDE] 收到空响应")
                        return False

            except socket.timeout:
                print(f"[SLIDE] 尝试 {attempt + 1} 超时 ({timeout}s)")
                if attempt < retry_count - 1:
                    time.sleep(1)
            except ConnectionRefusedError:
                print(f"[SLIDE] 尝试 {attempt + 1} 连接被拒绝")
                if attempt < retry_count - 1:
                    time.sleep(2)
            except Exception as e:
                print(f"[SLIDE] 尝试 {attempt + 1} 发生错误: {e}")
                if attempt < retry_count - 1:
                    time.sleep(1)
        
        print(f"[SLIDE] TCP指令发送失败: 所有 {retry_count} 次尝试都失败")
        return False

# ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

def send_cmd(sock, cmd, log_prefix="CMD", timeout=10):
    """发送指令到机器人 - 增强版本，支持超时控制"""
    try:
        original_timeout = sock.gettimeout()
        sock.settimeout(timeout)
        full_cmd = cmd + "\n"
        print(f"[{log_prefix}] SND: {full_cmd.strip()}")
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"[{log_prefix}] RCV: {response}")
        sock.settimeout(original_timeout)

        if not response:
            print(f"❌ 指令 '{cmd}' 收到空响应")
            return False

        parts = response.split(',')
        if not parts:
            print(f"❌ 指令 '{cmd}' 响应格式错误")
            return False

        error_id_str = parts[0]
        if error_id_str == '0':
            return True
        else:
            print(f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}")
            return False

    except socket.timeout:
        print(f"❌ 发送指令 '{cmd}' 超时 ({timeout}s)")
        return False
    except socket.error as e:
        print(f"❌ 发送指令 '{cmd}' 时网络错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 发送指令 '{cmd}' 时发生未知错误: {e}")
        return False

# ... (RobotControlApp 类的其他代码保持不变)
class RobotControlApp(ctk.CTk):
    def __init__(self):
        super().__init__()
        self.title("机器人与精准滑轨控制系统 (MG400) - V17.1")
        self.geometry("1200x800")  # 增加宽度，减少高度

        # 滑轨控制相关变量
        self.current_conveyor_position = 0.0
        self.conveyor_total_length = 850.0  # 更新为实际滑轨长度
        self.target_position = 0.0

        # 网络连接
        self.dashboard_socket = None
        self.motion_socket = None
        self.vision_queue = queue.Queue()
        self.is_robot_connected = False

        # 初始化DobotStudio Pro滑轨控制器
        self.slide_controller = DobotSlideRailController(ROBOT_IP)

        # UI和线程初始化
        self.create_widgets()
        self.update_conveyor_display()
        self.update_position_record_display()  # 更新位置记录显示
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()
        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 权限检查
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        if is_admin:
            self.log("✅ 程序正以【管理员权限】运行。", "lightgreen")
        else:
            self.log("⚠️ 程序正以【普通用户权限】运行。", "orange")

        # 尝试连接滑轨控制器
        self.log("🔗 正在连接Dobot滑轨控制器(纯TCP模式)...", "cyan")
        if self.slide_controller.connect():
            self.log("✅ 滑轨控制器连接成功！", "green")
        else:
            self.log("⚠️ 滑轨控制器连接失败，将使用传统控制模式。", "orange")

    def create_widgets(self):
        # 主布局框架
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=5, pady=5, fill="both", expand=True)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        # 左侧滚动控制面板
        self.left_frame = ctk.CTkScrollableFrame(self.main_frame, width=580, height=750,
                                                fg_color="transparent")
        self.left_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        
        # ... (机器人人机控制UI代码无变化)
        manual_control_frame = ctk.CTkFrame(self.left_frame)
        manual_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(manual_control_frame, text="机器人人机控制", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=2, pady=10)
        btn_x_plus = ctk.CTkButton(manual_control_frame, text="X+"); btn_x_minus = ctk.CTkButton(manual_control_frame, text="X-"); btn_y_plus = ctk.CTkButton(manual_control_frame, text="Y+"); btn_y_minus = ctk.CTkButton(manual_control_frame, text="Y-"); btn_z_plus = ctk.CTkButton(manual_control_frame, text="Z+"); btn_z_minus = ctk.CTkButton(manual_control_frame, text="Z-")
        btn_x_plus.grid(row=1, column=0, padx=5, pady=5, sticky="ew"); btn_x_minus.grid(row=1, column=1, padx=5, pady=5, sticky="ew"); btn_y_plus.grid(row=2, column=0, padx=5, pady=5, sticky="ew"); btn_y_minus.grid(row=2, column=1, padx=5, pady=5, sticky="ew"); btn_z_plus.grid(row=3, column=0, padx=5, pady=5, sticky="ew"); btn_z_minus.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        btn_x_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("X+")); btn_x_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("X-")); btn_y_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y+")); btn_y_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y-")); btn_z_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z+")); btn_z_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z-"))
        for btn in [btn_x_plus, btn_x_minus, btn_y_plus, btn_y_minus, btn_z_plus, btn_z_minus]: btn.bind("<ButtonRelease-1>", self.stop_jog)
        self.btn_home = ctk.CTkButton(manual_control_frame, text="回原点", command=self.go_home); self.btn_home.grid(row=4, column=0, columnspan=2, pady=10, padx=5, sticky="ew")

        # ▼▼▼▼▼ 【精准滑轨控制UI - V17.0 全面升级】 ▼▼▼▼▼
        conveyor_frame = ctk.CTkFrame(self.left_frame)
        conveyor_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(conveyor_frame, text="精准滑轨控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 位置显示区域
        pos_display_frame = ctk.CTkFrame(conveyor_frame)
        pos_display_frame.pack(pady=5, padx=5, fill="x")

        # 当前位置显示
        current_pos_frame = ctk.CTkFrame(pos_display_frame, fg_color="transparent")
        current_pos_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(current_pos_frame, text="当前位置:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_conveyor_pos = ctk.CTkLabel(current_pos_frame, text="0.00 mm",
                                              font=ctk.CTkFont(size=14, weight="bold"), text_color="lightgreen")
        self.label_conveyor_pos.pack(side="left", padx=5)

        # 目标位置显示
        target_pos_frame = ctk.CTkFrame(pos_display_frame, fg_color="transparent")
        target_pos_frame.pack(pady=2, fill="x")
        ctk.CTkLabel(target_pos_frame, text="目标位置:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_target_pos = ctk.CTkLabel(target_pos_frame, text="-- mm",
                                            font=ctk.CTkFont(size=14), text_color="orange")
        self.label_target_pos.pack(side="left", padx=5)

        # 进度条
        self.progress_conveyor = ctk.CTkProgressBar(conveyor_frame)
        self.progress_conveyor.pack(pady=5, padx=5, fill="x")

        # 步进控制区域
        step_control_frame = ctk.CTkFrame(conveyor_frame)
        step_control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(step_control_frame, text="步进控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        step_input_frame = ctk.CTkFrame(step_control_frame, fg_color="transparent")
        step_input_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(step_input_frame, text="步进距离(mm):").pack(side="left", padx=(0, 5))
        self.entry_conveyor_step_dist = ctk.CTkEntry(step_input_frame, placeholder_text="例如: 50", width=100)
        self.entry_conveyor_step_dist.pack(side="left", padx=5)
        self.entry_conveyor_step_dist.insert(0, "50")

        step_btn_frame = ctk.CTkFrame(step_control_frame, fg_color="transparent")
        step_btn_frame.pack(pady=5, fill="x")
        self.btn_conveyor_bwd = ctk.CTkButton(step_btn_frame, text="◀ 后退", command=lambda: self.move_conveyor_step(-1), width=80)
        self.btn_conveyor_bwd.pack(side="left", padx=5)
        self.btn_conveyor_fwd = ctk.CTkButton(step_btn_frame, text="前进 ▶", command=lambda: self.move_conveyor_step(1), width=80)
        self.btn_conveyor_fwd.pack(side="left", padx=5)

        # 绝对位置控制区域
        abs_control_frame = ctk.CTkFrame(conveyor_frame)
        abs_control_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(abs_control_frame, text="绝对位置控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        abs_input_frame = ctk.CTkFrame(abs_control_frame, fg_color="transparent")
        abs_input_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(abs_input_frame, text="目标位置(mm):").pack(side="left", padx=(0, 5))
        self.entry_target_position = ctk.CTkEntry(abs_input_frame, placeholder_text="0-850", width=100)
        self.entry_target_position.pack(side="left", padx=5)
        self.btn_move_to_position = ctk.CTkButton(abs_input_frame, text="移动到此位置",
                                                 command=self.move_to_absolute_position, width=120)
        self.btn_move_to_position.pack(side="left", padx=5)

        # 快速位置按钮
        quick_pos_frame = ctk.CTkFrame(abs_control_frame, fg_color="transparent")
        quick_pos_frame.pack(pady=5, fill="x")
        quick_positions = [0, 200, 400, 600, 850]
        for pos in quick_positions:
            btn = ctk.CTkButton(quick_pos_frame, text=f"{pos}mm", width=60,
                               command=lambda p=pos: self.move_to_quick_position(p))
            btn.pack(side="left", padx=2)

        # 标定和设置区域
        calib_frame = ctk.CTkFrame(conveyor_frame)
        calib_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(calib_frame, text="标定与设置", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        calib_btn_frame = ctk.CTkFrame(calib_frame, fg_color="transparent")
        calib_btn_frame.pack(pady=5, fill="x")
        self.btn_calib_conveyor = ctk.CTkButton(calib_btn_frame, text="标定零点",
                                               command=self.calibrate_conveyor_zero, width=80)
        self.btn_calib_conveyor.pack(side="left", padx=5)
        self.btn_find_limits = ctk.CTkButton(calib_btn_frame, text="检测限位",
                                            command=self.find_slide_limits, width=80)
        self.btn_find_limits.pack(side="left", padx=5)
        self.btn_emergency_stop = ctk.CTkButton(calib_btn_frame, text="急停",
                                               command=self.emergency_stop, width=60,
                                               fg_color="red", hover_color="darkred")
        self.btn_emergency_stop.pack(side="left", padx=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【位置记录和验证系统 - V17.1 新增】 ▼▼▼▼▼
        position_record_frame = ctk.CTkFrame(self.left_frame)
        position_record_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(position_record_frame, text="位置记录和验证系统",
                    font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)

        # 记录进度显示
        progress_frame = ctk.CTkFrame(position_record_frame)
        progress_frame.pack(pady=5, padx=5, fill="x")

        progress_info_frame = ctk.CTkFrame(progress_frame, fg_color="transparent")
        progress_info_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(progress_info_frame, text="记录进度:", font=ctk.CTkFont(size=12)).pack(side="left")
        self.label_record_progress = ctk.CTkLabel(progress_info_frame, text="0/4",
                                                 font=ctk.CTkFont(size=12, weight="bold"), text_color="orange")
        self.label_record_progress.pack(side="left", padx=5)

        self.progress_record = ctk.CTkProgressBar(progress_frame)
        self.progress_record.pack(pady=5, padx=5, fill="x")
        self.progress_record.set(0)

        # 四个点位记录区域
        zones_frame = ctk.CTkFrame(position_record_frame)
        zones_frame.pack(pady=5, padx=5, fill="x")

        # 检测区域
        detection_frame = ctk.CTkFrame(zones_frame)
        detection_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(detection_frame, text="检测区域", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 检测区一
        det1_frame = ctk.CTkFrame(detection_frame, fg_color="transparent")
        det1_frame.pack(pady=2, fill="x")
        self.label_det1_status = ctk.CTkLabel(det1_frame, text="检测区一: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_det1_status.pack(side="left")
        self.btn_record_det1 = ctk.CTkButton(det1_frame, text="记录检测区一", width=100,
                                            command=lambda: self.record_position("detection_zone_1"))
        self.btn_record_det1.pack(side="right", padx=5)
        self.btn_verify_det1 = ctk.CTkButton(det1_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("detection_zone_1"),
                                            state="disabled")
        self.btn_verify_det1.pack(side="right", padx=2)

        # 检测区二
        det2_frame = ctk.CTkFrame(detection_frame, fg_color="transparent")
        det2_frame.pack(pady=2, fill="x")
        self.label_det2_status = ctk.CTkLabel(det2_frame, text="检测区二: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_det2_status.pack(side="left")
        self.btn_record_det2 = ctk.CTkButton(det2_frame, text="记录检测区二", width=100,
                                            command=lambda: self.record_position("detection_zone_2"))
        self.btn_record_det2.pack(side="right", padx=5)
        self.btn_verify_det2 = ctk.CTkButton(det2_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("detection_zone_2"),
                                            state="disabled")
        self.btn_verify_det2.pack(side="right", padx=2)

        # 装配区域
        assembly_frame = ctk.CTkFrame(zones_frame)
        assembly_frame.pack(pady=5, fill="x")
        ctk.CTkLabel(assembly_frame, text="装配区域", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        # 装配区一
        asm1_frame = ctk.CTkFrame(assembly_frame, fg_color="transparent")
        asm1_frame.pack(pady=2, fill="x")
        self.label_asm1_status = ctk.CTkLabel(asm1_frame, text="装配区一: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_asm1_status.pack(side="left")
        self.btn_record_asm1 = ctk.CTkButton(asm1_frame, text="记录装配区一", width=100,
                                            command=lambda: self.record_position("assembly_zone_1"))
        self.btn_record_asm1.pack(side="right", padx=5)
        self.btn_verify_asm1 = ctk.CTkButton(asm1_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("assembly_zone_1"),
                                            state="disabled")
        self.btn_verify_asm1.pack(side="right", padx=2)

        # 装配区二
        asm2_frame = ctk.CTkFrame(assembly_frame, fg_color="transparent")
        asm2_frame.pack(pady=2, fill="x")
        self.label_asm2_status = ctk.CTkLabel(asm2_frame, text="装配区二: 未记录",
                                             font=ctk.CTkFont(size=12), text_color="gray")
        self.label_asm2_status.pack(side="left")
        self.btn_record_asm2 = ctk.CTkButton(asm2_frame, text="记录装配区二", width=100,
                                            command=lambda: self.record_position("assembly_zone_2"))
        self.btn_record_asm2.pack(side="right", padx=5)
        self.btn_verify_asm2 = ctk.CTkButton(asm2_frame, text="验证", width=60,
                                            command=lambda: self.verify_position("assembly_zone_2"),
                                            state="disabled")
        self.btn_verify_asm2.pack(side="right", padx=2)

        # 批量操作按钮
        batch_frame = ctk.CTkFrame(position_record_frame)
        batch_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(batch_frame, text="批量操作", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)

        batch_btn_frame = ctk.CTkFrame(batch_frame, fg_color="transparent")
        batch_btn_frame.pack(pady=5, fill="x")

        self.btn_verify_all = ctk.CTkButton(batch_btn_frame, text="验证所有位置", width=100,
                                           command=self.verify_all_positions, state="disabled")
        self.btn_verify_all.pack(side="left", padx=5)

        self.btn_clear_all = ctk.CTkButton(batch_btn_frame, text="清除所有记录", width=100,
                                          command=self.clear_all_records, fg_color="orange", hover_color="darkorange")
        self.btn_clear_all.pack(side="left", padx=5)

        self.btn_export_positions = ctk.CTkButton(batch_btn_frame, text="导出位置", width=80,
                                                 command=self.export_positions)
        self.btn_export_positions.pack(side="left", padx=5)

        # 网络诊断按钮
        self.btn_network_diag = ctk.CTkButton(batch_btn_frame, text="网络诊断", width=80,
                                             command=self.network_diag_click, fg_color="purple", hover_color="darkviolet")
        self.btn_network_diag.pack(side="left", padx=5)

        # 验证结果显示
        self.label_verify_result = ctk.CTkLabel(position_record_frame, text="",
                                               font=ctk.CTkFont(size=12))
        self.label_verify_result.pack(pady=5)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ... (后续UI代码无变化)
        vision_control_frame = ctk.CTkFrame(self.left_frame); vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="视觉控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkButton(vision_control_frame, text="拍照", command=self.trigger_vision_capture).pack(pady=5, padx=5, fill="x")
        connect_frame = ctk.CTkFrame(self.left_frame); connect_frame.pack(pady=20, padx=10, fill="x", side="bottom")
        self.connect_label = ctk.CTkLabel(connect_frame, text="机器人未连接", text_color="orange"); self.connect_label.pack(side="left", padx=10)
        self.btn_connect = ctk.CTkButton(connect_frame, text="连接机器人", command=self.handle_connect_button_click); self.btn_connect.pack(side="right", padx=10)
        self.auto_run_switch = ctk.CTkSwitch(connect_frame, text="自动抓取", onvalue=True, offvalue=False); self.auto_run_switch.pack(side="right", padx=10)
        # 右侧监控和日志面板
        self.right_frame = ctk.CTkFrame(self.main_frame, width=580)
        self.right_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        ctk.CTkLabel(self.right_frame, text="监控界面", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        self.image_display_label = ctk.CTkLabel(self.right_frame, text="[等待视觉软件发送图像...]", bg_color="grey30", height=300); self.image_display_label.pack(pady=10, padx=10, fill="both", expand=True)
        ctk.CTkLabel(self.right_frame, text="信息显示/日志", font=ctk.CTkFont(size=14)).pack()
        self.log_textbox = ctk.CTkTextbox(self.right_frame, state="disabled", height=200); self.log_textbox.pack(pady=10, padx=10, fill="both", expand=True)


    # ▼▼▼▼▼ 【精准滑轨控制函数 - V17.0 全面升级】 ▼▼▼▼▼
    def update_conveyor_display(self):
        """刷新滑轨位置的UI显示"""
        self.label_conveyor_pos.configure(text=f"{self.current_conveyor_position:.2f} mm")
        self.label_target_pos.configure(text=f"{self.target_position:.2f} mm")
        progress = self.current_conveyor_position / self.conveyor_total_length if self.conveyor_total_length > 0 else 0
        self.progress_conveyor.set(max(0, min(1, progress)))

    def calibrate_conveyor_zero(self):
        """标定当前位置为零点"""
        self.log("⚙️ 正在标定滑轨零点...", "yellow")
        self.current_conveyor_position = 0.0
        self.target_position = 0.0
        self.slide_controller.current_position = 0.0
        self.update_conveyor_display()
        self.log("✅ 滑轨零点标定完成。", "green")

    def move_conveyor_step(self, direction):
        """按步进距离移动滑轨 (1: 前进, -1: 后退)"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        step_dist_str = self.entry_conveyor_step_dist.get()
        try:
            step_distance = abs(float(step_dist_str))

            if direction > 0:
                target_position = self.current_conveyor_position + step_distance
                log_action = "前进"
            else:
                target_position = self.current_conveyor_position - step_distance
                log_action = "后退"

            # 检查范围限制
            if not (0 <= target_position <= self.conveyor_total_length):
                self.log(f"❌ 目标位置 {target_position:.2f} mm 超出范围 (0-{self.conveyor_total_length} mm)", "red")
                return

            self.target_position = target_position
            self.update_conveyor_display()
            self.log(f"🎯 准备{log_action} {step_distance} mm 到位置 {target_position:.2f} mm...", "cyan")

            # 使用DobotStudio Pro精确控制
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(target_position)
            else:
                # 回退到传统控制模式
                self.log("⚠️ 使用传统MovJExt模式移动...", "orange")
                distance_to_move = step_distance * direction
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})" # 注意：传统模式下可能是相对移动
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = target_position
                self.update_conveyor_display()
                self.log(f"✅ 滑轨精确移动完成。当前位置: {self.current_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 滑轨移动失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{step_dist_str}' 不是有效的步进距离。", "red")
        except Exception as e:
            self.log(f"❌ 移动滑轨时发生错误: {e}", "red")

    def move_to_absolute_position(self):
        """移动到绝对位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        target_str = self.entry_target_position.get()
        try:
            target_position = float(target_str)

            # 检查范围限制
            if not (0 <= target_position <= self.conveyor_total_length):
                self.log(f"❌ 目标位置 {target_position:.2f} mm 超出范围 (0-{self.conveyor_total_length} mm)", "red")
                return

            self.target_position = target_position
            self.update_conveyor_display()
            self.log(f"🎯 正在移动到绝对位置 {target_position:.2f} mm...", "cyan")

            # 使用DobotStudio Pro精确控制
            if self.slide_controller.is_connected:
                success = self.slide_controller.move_to_absolute_position(target_position)
            else:
                # 回退到传统控制模式
                self.log("⚠️ 使用传统MovJExt模式移动...", "orange")
                distance_to_move = target_position - self.current_conveyor_position
                cmd = f"MovJExt({distance_to_move},{{SYNC=1}})" # 注意：传统模式下可能是相对移动
                success = send_cmd(self.motion_socket, cmd, "MOT")

            if success:
                self.current_conveyor_position = target_position
                self.update_conveyor_display()
                self.log(f"✅ 已到达目标位置: {self.current_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 移动到绝对位置失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{target_str}' 不是有效的位置值。", "red")
        except Exception as e:
            self.log(f"❌ 移动到绝对位置时发生错误: {e}", "red")

    def move_to_quick_position(self, position):
        """快速移动到预设位置"""
        self.entry_target_position.delete(0, 'end')
        self.entry_target_position.insert(0, str(position))
        self.move_to_absolute_position()

    def find_slide_limits(self):
        """检测滑轨限位"""
        self.log("🔍 开始检测滑轨限位...", "yellow")
        self.log("⚠️ 此功能需要硬件限位开关支持", "orange")
        # 这里可以实现限位检测逻辑
        # 目前只是提示功能

    def emergency_stop(self):
        """紧急停止"""
        self.log("🛑 执行紧急停止！", "red")
        if self.slide_controller.is_connected:
            if not self.slide_controller.emergency_stop():
                 self.log("滑轨控制器急停失败，尝试主控急停...", "orange")
        if self.motion_socket:
            send_cmd(self.motion_socket, "Pause()", "MOT")
        self.log("✅ 紧急停止指令已发送。", "orange")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

    # ▼▼▼▼▼ 【位置记录和验证功能 - V17.1 新增】 ▼▼▼▼▼
    def update_position_record_display(self):
        """更新位置记录显示"""
        recorded_count = position_manager.get_all_recorded_count()
        total_count = 4

        # 更新进度显示
        self.label_record_progress.configure(text=f"{recorded_count}/{total_count}")
        progress = recorded_count / total_count
        self.progress_record.set(progress)

        # 更新各区域状态显示
        zones = [
            ("detection_zone_1", self.label_det1_status, self.btn_verify_det1),
            ("detection_zone_2", self.label_det2_status, self.btn_verify_det2),
            ("assembly_zone_1", self.label_asm1_status, self.btn_verify_asm1),
            ("assembly_zone_2", self.label_asm2_status, self.btn_verify_asm2)
        ]

        for zone_key, status_label, verify_btn in zones:
            zone_name = position_manager.get_zone_display_name(zone_key)

            if position_manager.is_recorded(zone_key):
                position = position_manager.get_position(zone_key)
                record_time = position_manager.get_record_time(zone_key)
                status_label.configure(
                    text=f"{zone_name}: {position:.2f}mm ({record_time})",
                    text_color="lightgreen"
                )
                verify_btn.configure(state="normal")
            else:
                status_label.configure(
                    text=f"{zone_name}: 未记录",
                    text_color="gray"
                )
                verify_btn.configure(state="disabled")

        # 更新批量验证按钮状态
        if recorded_count > 0:
            self.btn_verify_all.configure(state="normal")
        else:
            self.btn_verify_all.configure(state="disabled")

    def record_position(self, zone_key: str):
        """记录指定区域的位置"""
        zone_name = position_manager.get_zone_display_name(zone_key)
        current_pos = self.current_conveyor_position

        self.log(f"📍 正在记录{zone_name}位置: {current_pos:.2f} mm...", "cyan")

        success = position_manager.record_position(zone_key, current_pos)

        if success:
            self.log(f"✅ {zone_name}位置记录成功: {current_pos:.2f} mm", "green")
            self.update_position_record_display()

            # 检查是否所有位置都已记录
            if position_manager.is_all_recorded():
                self.log("🎉 所有位置记录完成！可以开始验证。", "lightgreen")
        else:
            self.log(f"❌ {zone_name}位置记录失败", "red")

    def verify_position(self, zone_key: str):
        """验证指定区域的位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        zone_name = position_manager.get_zone_display_name(zone_key)
        expected_position = position_manager.get_position(zone_key)

        if expected_position is None:
            self.log(f"❌ {zone_name}尚未记录位置", "red")
            return

        self.log(f"🎯 验证{zone_name}位置，移动到 {expected_position:.2f} mm...", "cyan")

        # 移动到记录的位置
        if self.slide_controller.is_connected:
            success = self.slide_controller.move_to_absolute_position(expected_position)
        else:
            self.log("⚠️ 使用传统MovJExt模式移动...", "orange")
            distance_to_move = expected_position - self.current_conveyor_position
            cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
            success = send_cmd(self.motion_socket, cmd, "MOT")

        if success:
            # 更新当前位置
            self.current_conveyor_position = expected_position
            self.update_conveyor_display()

            # 验证位置精度
            actual_position = self.current_conveyor_position
            is_valid, error = position_manager.validate_position(zone_key, actual_position)

            if is_valid:
                self.log(f"✅ {zone_name}位置验证成功！误差: {error:.2f} mm", "green")
                self.label_verify_result.configure(
                    text=f"✅ {zone_name}: 期望 {expected_position:.2f}mm, 实际 {actual_position:.2f}mm, 误差 {error:.2f}mm",
                    text_color="green"
                )
            else:
                self.log(f"⚠️ {zone_name}位置验证警告！误差: {error:.2f} mm (超出±{position_manager.tolerance_mm}mm)", "orange")
                self.label_verify_result.configure(
                    text=f"⚠️ {zone_name}: 期望 {expected_position:.2f}mm, 实际 {actual_position:.2f}mm, 误差 {error:.2f}mm",
                    text_color="orange"
                )
        else:
            self.log(f"❌ 移动到{zone_name}失败", "red")
            self.label_verify_result.configure(
                text=f"❌ {zone_name}验证失败：移动失败",
                text_color="red"
            )

    def verify_all_positions(self):
        """验证所有已记录的位置"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return

        self.log("🔍 开始验证所有已记录位置...", "cyan")

        zones = ["detection_zone_1", "detection_zone_2", "assembly_zone_1", "assembly_zone_2"]
        verification_results = []

        for zone_key in zones:
            if position_manager.is_recorded(zone_key):
                zone_name = position_manager.get_zone_display_name(zone_key)
                expected_position = position_manager.get_position(zone_key)

                self.log(f"🎯 验证{zone_name}...", "cyan")

                # 移动到位置
                if self.slide_controller.is_connected:
                    success = self.slide_controller.move_to_absolute_position(expected_position)
                else:
                    self.log("⚠️ 使用传统MovJExt模式移动...", "orange")
                    distance_to_move = expected_position - self.current_conveyor_position
                    cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
                    success = send_cmd(self.motion_socket, cmd, "MOT")

                if success:
                    self.current_conveyor_position = expected_position
                    actual_position = self.current_conveyor_position
                    is_valid, error = position_manager.validate_position(zone_key, actual_position)

                    verification_results.append({
                        "zone": zone_name,
                        "expected": expected_position,
                        "actual": actual_position,
                        "error": error,
                        "valid": is_valid
                    })

                    if is_valid:
                        self.log(f"✅ {zone_name}: 误差 {error:.2f}mm", "green")
                    else:
                        self.log(f"⚠️ {zone_name}: 误差 {error:.2f}mm (超出容差)", "orange")
                else:
                    self.log(f"❌ {zone_name}: 移动失败", "red")
                    verification_results.append({
                        "zone": zone_name,
                        "expected": expected_position,
                        "actual": None,
                        "error": float('inf'),
                        "valid": False
                    })

                # 等待移动完成
                time.sleep(1)

        # 显示验证摘要
        valid_count = sum(1 for r in verification_results if r["valid"])
        total_count = len(verification_results)

        if valid_count == total_count:
            self.log(f"🎉 所有位置验证通过！({valid_count}/{total_count})", "lightgreen")
            self.label_verify_result.configure(
                text=f"🎉 批量验证完成：{valid_count}/{total_count} 通过",
                text_color="green"
            )
        else:
            self.log(f"⚠️ 位置验证完成：{valid_count}/{total_count} 通过", "orange")
            self.label_verify_result.configure(
                text=f"⚠️ 批量验证完成：{valid_count}/{total_count} 通过",
                text_color="orange"
            )

        self.update_conveyor_display()

    def clear_all_records(self):
        """清除所有位置记录"""
        self.log("🗑️ 清除所有位置记录...", "yellow")
        position_manager.clear_all_records()
        self.update_position_record_display()
        self.label_verify_result.configure(text="")
        self.log("✅ 所有位置记录已清除", "green")

    def export_positions(self):
        """导出位置数据"""
        success = position_manager.export_positions()
        if success:
            self.log("✅ 位置数据导出成功", "green")
        else:
            self.log("❌ 位置数据导出失败", "red")

    def run_network_diagnostics(self):
        """运行网络诊断"""
        self.log("🔍 开始网络诊断...", "cyan")

        # 测试基本网络连通性
        self.log("1. 测试网络连通性...", "yellow")
        try:
            import subprocess
            result = subprocess.run(['ping', '-n', '3', '-w', '3000', ROBOT_IP],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                self.log(f"✅ 网络连通性正常 - 可以ping通 {ROBOT_IP}", "green")
            else:
                self.log(f"❌ 网络连通性失败 - 无法ping通 {ROBOT_IP}", "red")
                self.log("请检查：", "yellow")
                self.log("  - 机器人是否开机", "yellow")
                self.log("  - 网络线是否连接", "yellow")
                self.log("  - IP地址是否正确", "yellow")
        except Exception as e:
            self.log(f"❌ 网络连通性测试异常: {e}", "red")

        # 测试端口连通性
        self.log("2. 测试端口连通性...", "yellow")
        ports_to_test = [
            (DASHBOARD_PORT, "Dashboard端口"),
            (MOTION_PORT, "Motion端口")
        ]

        for port, description in ports_to_test:
            try:
                test_socket = socket.create_connection((ROBOT_IP, port), timeout=5)
                test_socket.close()
                self.log(f"✅ {description} ({port}) 连接正常", "green")
            except socket.timeout:
                self.log(f"❌ {description} ({port}) 连接超时", "red")
            except ConnectionRefusedError:
                self.log(f"❌ {description} ({port}) 连接被拒绝", "red")
            except Exception as e:
                self.log(f"❌ {description} ({port}) 连接异常: {e}", "red")

        # 测试滑轨控制器连接
        self.log("3. 测试滑轨控制器连接...", "yellow")
        if self.slide_controller.connect():
            self.log("✅ 滑轨控制器连接正常", "green")
        else:
            self.log("❌ 滑轨控制器连接失败", "red")

        # 显示当前网络配置
        self.log("4. 当前网络配置:", "yellow")
        self.log(f"  机器人IP: {ROBOT_IP}", "white")
        self.log(f"  本机IP: {PYTHON_PC_IP}", "white")
        self.log(f"  Dashboard端口: {DASHBOARD_PORT}", "white")
        self.log(f"  Motion端口: {MOTION_PORT}", "white")
        self.log(f"  视觉服务端口: {VISION_SERVER_PORT}", "white")

        self.log("🔍 网络诊断完成", "cyan")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
    
    # (其他函数无变化...)
    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal"); self.log_textbox.tag_config(f"tag_{color}", foreground=color); self.log_textbox.insert("end", f"{message}\n", f"tag_{color}"); self.log_textbox.configure(state="disabled"); self.log_textbox.see("end")
    def handle_connect_button_click(self):
        if not self.is_robot_connected:
            try:
                self.log(f"正在连接机器人 at {ROBOT_IP}...", "cyan")

                # 增加连接超时时间并添加详细日志
                self.log("正在建立Dashboard连接...", "yellow")
                self.dashboard_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=10)
                self.dashboard_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                self.log("✅ Dashboard连接成功", "green")

                self.log("正在建立Motion连接...", "yellow")
                self.motion_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=10)
                self.motion_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                self.log("✅ Motion连接成功", "green")

                self.log("正在尝试使能机器人...", "yellow")
                if not send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"):
                    raise ConnectionError("机器人使能失败")

                self.log("机器人已使能，等待伺服系统稳定...", "yellow")
                time.sleep(2)  # 增加等待时间

                self.is_robot_connected = True
                self.log("✅ 机器人连接并使能成功!", "green")
                self.connect_label.configure(text="机器人已连接", text_color="green")
                self.btn_connect.configure(text="断开连接")

            except socket.timeout:
                self.log(f"❌ 连接超时: 无法在10秒内连接到机器人", "red")
                if hasattr(self, 'dashboard_socket') and self.dashboard_socket:
                    self.dashboard_socket.close()
                if hasattr(self, 'motion_socket') and self.motion_socket:
                    self.motion_socket.close()
            except ConnectionRefusedError:
                self.log(f"❌ 连接被拒绝: 机器人可能未启动或端口被占用", "red")
                if hasattr(self, 'dashboard_socket') and self.dashboard_socket:
                    self.dashboard_socket.close()
                if hasattr(self, 'motion_socket') and self.motion_socket:
                    self.motion_socket.close()
            except Exception as e:
                self.log(f"❌ 连接失败: {e}", "red")
                if hasattr(self, 'dashboard_socket') and self.dashboard_socket:
                    self.dashboard_socket.close()
                if hasattr(self, 'motion_socket') and self.motion_socket:
                    self.motion_socket.close()
        else:
            self.log("正在断开机器人连接...", "yellow")
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            if self.dashboard_socket:
                self.dashboard_socket.close()
                self.dashboard_socket = None
            if self.motion_socket:
                self.motion_socket.close()
                self.motion_socket = None
            self.is_robot_connected = False
            self.log("🔌 机器人已断开。", "orange")
            self.connect_label.configure(text="机器人未连接", text_color="orange")
            self.btn_connect.configure(text="连接机器人")
    def trigger_vision_capture(self):
        self.log("📸 发送拍照触发指令...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"; s.sendall(cmd_to_send.encode('utf-8')); self.log("✅ 触发指令已发送成功。", "green")
        except socket.timeout: self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e: self.log(f"❌ 触发失败: {e}", "red")
    def start_jog(self, axis_id):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log(f"🤖 开始点动: {axis_id}", "cyan"); send_cmd(self.motion_socket, f"MoveJog({axis_id})", "MOT")
    def stop_jog(self, event=None):
        if not self.is_robot_connected: return
        self.log("🤖 停止点动", "cyan"); send_cmd(self.motion_socket, "MoveJog()", "MOT"); time.sleep(0.2)
    def go_home(self):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log("🤖 正在移动到安全原点...");
        if send_cmd(self.motion_socket, "MoveJ(200, 0, 50, 0)", "MOT"):
            send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("✅ 已到达原点。")
        else: self.log("❌ 回原点失败。", "red")
    def execute_pick_and_place(self, target_x, target_y, target_r):
        if not self.is_robot_connected: self.log("⚠️ 自动抓取失败：机器人未连接", "orange"); return
        self.log(f"🤖 开始执行抓取任务..."); pickup_z_high, pickup_z_low = 50, 10; place_x, place_y, place_z = 150, -150, 50
        try:
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.motion_socket, f"MoveJ({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_low}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("🤏 抓取: 闭合夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH") 
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveJ({place_x}, {place_y}, {place_z}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("👐 放置: 张开夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            self.go_home(); self.log("✅ 抓取任务完成!", "green")
        except Exception as e: self.log(f"❌ 机器人执行动作时出错: {e}", "red")
    def process_vision_queue(self):
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包: {message}", "cyan")
                parts = message.split(';')
                image_path = parts[-1].strip()
                self.show_image_from_path(image_path)
                if len(parts) >= 2 and self.auto_run_switch.get() and self.is_robot_connected:
                    coord_data = parts[0]
                    try:
                        coord_parts = coord_data.split(',');
                        if len(coord_parts) >= 2:
                            robot_x, robot_y = float(coord_parts[0]), float(coord_parts[1]); robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                            self.execute_pick_and_place(robot_x, robot_y, robot_r)
                        else: self.log(f"⚠️ 坐标部分格式无法解析: {coord_data}", "orange")
                    except (ValueError, IndexError) as e: self.log(f"❌ 解析坐标数据失败: {e}", "red")
        except queue.Empty: pass
        self.after(100, self.process_vision_queue)
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")
    def vision_listener_thread(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT)); s.listen()
                print(f"👂 视觉服务器已启动，正在监听端口 {VISION_SERVER_PORT}...")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        print(f"🤝 视觉软件已连接: {addr}")
                        while True:
                            data = conn.recv(1024)
                            if not data: print("🔌 视觉软件已断开。"); break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e: print(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")
    def on_closing(self):
        if self.is_robot_connected:
            self.log("正在断开机器人连接..."); send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close()
        self.destroy()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = RobotControlApp()
    app.mainloop()