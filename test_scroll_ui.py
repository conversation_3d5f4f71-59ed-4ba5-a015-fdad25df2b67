#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滚动界面功能
"""

import customtkinter as ctk

def test_scroll_interface():
    """测试滚动界面"""
    
    # 创建主窗口
    root = ctk.CTk()
    root.title("滚动界面测试 - V17.0")
    root.geometry("1200x900")
    
    # 主框架
    main_frame = ctk.CTkFrame(root)
    main_frame.pack(padx=10, pady=10, fill="both", expand=True)
    main_frame.grid_columnconfigure(0, weight=1)
    main_frame.grid_columnconfigure(1, weight=1)
    main_frame.grid_rowconfigure(0, weight=1)
    
    # 左侧滚动框架
    left_scroll_frame = ctk.CTkScrollableFrame(main_frame, label_text="控制面板")
    left_scroll_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
    left_scroll_frame.grid_columnconfigure(0, weight=1)
    
    # 右侧滚动框架
    right_scroll_frame = ctk.CTkScrollableFrame(main_frame, label_text="监控界面")
    right_scroll_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
    right_scroll_frame.grid_columnconfigure(0, weight=1)
    
    # 在左侧添加多个控制组件来测试滚动
    for i in range(1, 11):
        frame = ctk.CTkFrame(left_scroll_frame)
        frame.pack(pady=10, padx=10, fill="x")
        
        ctk.CTkLabel(frame, text=f"控制组 {i}", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        
        # 添加一些按钮
        btn_frame = ctk.CTkFrame(frame, fg_color="transparent")
        btn_frame.pack(pady=5, padx=5, fill="x")
        
        for j in range(3):
            btn = ctk.CTkButton(btn_frame, text=f"按钮 {j+1}", width=80)
            btn.pack(side="left", padx=5)
        
        # 添加输入框
        entry = ctk.CTkEntry(frame, placeholder_text=f"输入框 {i}")
        entry.pack(pady=5, padx=5, fill="x")
        
        # 添加进度条
        progress = ctk.CTkProgressBar(frame)
        progress.pack(pady=5, padx=5, fill="x")
        progress.set(i * 0.1)
    
    # 在右侧添加监控内容
    # 图像显示区域
    image_label = ctk.CTkLabel(right_scroll_frame, text="[图像显示区域]", bg_color="grey30", height=300)
    image_label.pack(pady=10, padx=10, fill="x")
    
    # 添加多个信息显示区域来测试滚动
    for i in range(1, 8):
        info_frame = ctk.CTkFrame(right_scroll_frame)
        info_frame.pack(pady=10, padx=10, fill="x")
        
        ctk.CTkLabel(info_frame, text=f"信息区域 {i}", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
        
        # 添加文本框
        textbox = ctk.CTkTextbox(info_frame, height=100)
        textbox.pack(pady=5, padx=5, fill="both", expand=True)
        textbox.insert("0.0", f"这是第 {i} 个信息显示区域的内容。\n" * 5)
        textbox.configure(state="disabled")
    
    # 添加说明标签
    info_label = ctk.CTkLabel(root, text="💡 提示：使用鼠标滚轮或拖动滚动条来上下滚动界面内容", 
                             font=ctk.CTkFont(size=12))
    info_label.pack(pady=5)
    
    # 运行测试
    root.mainloop()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    test_scroll_interface()
